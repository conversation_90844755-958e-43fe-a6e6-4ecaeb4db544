import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart'
    show PhotoUploadWidget;

class RadioButtonWidget extends StatefulWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final Widget? conditionalWidget; // Optional conditional widget to show
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final VoidCallback? onCameraTap;
  final bool isRequired;
  final List<String> selectedImages;
  final String? photoErrorText;

  const RadioButtonWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.conditionalWidget,
    this.showCameraIcon = false,
    this.isCameraMandatory = false,
    this.onCameraTap,
    this.isRequired = false,
    this.selectedImages = const [],
    this.photoErrorText,
  });

  @override
  State<RadioButtonWidget> createState() => _RadioButtonWidgetState();
}

class _RadioButtonWidgetState extends State<RadioButtonWidget> {
  final TextEditingController _conditionalTextController =
      TextEditingController();
  String? _conditionalText;

  @override
  void dispose() {
    _conditionalTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = widget.measurement.measurementOptions ?? [];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title with required indicator
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.measurement.measurementDescription ??
                      'Select an option',
                  style: textTheme.montserratTitleExtraSmall,
                ),
              ),
              if (widget.isRequired)
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.priority_high,
                    color: Colors.white,
                    size: 10,
                  ),
                ),
            ],
          ),
          const Gap(16),
          Row(
            children: options.map((option) {
              final optionId = option.measurementOptionId?.toString();
              final isSelected = widget.value == optionId;

              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    widget.onChanged(optionId);
                    // Check if this triggers conditional text field
                    if (optionId != null &&
                        option.measurementOptionDescription?.toLowerCase() ==
                            'no') {
                      // Show conditional text field for "No" option
                      setState(() {
                        _conditionalText = '';
                      });
                    } else {
                      // Hide conditional text field for other options
                      setState(() {
                        _conditionalText = null;
                        _conditionalTextController.clear();
                      });
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 12.0),
                    child: Row(
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primaryBlue
                                : Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppColors.primaryBlue
                                  : AppColors.blackTint2,
                              width: 2,
                            ),
                          ),
                          child: isSelected
                              ? Container(
                                  width: 8,
                                  height: 8,
                                  margin: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                )
                              : null,
                        ),
                        const Gap(8),
                        Expanded(
                          child: Text(
                            option.measurementOptionDescription ?? 'Option',
                            style: textTheme.montserratTitleExtraSmall.copyWith(
                              color: AppColors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          // Conditional widget (existing)
          if (widget.conditionalWidget != null) ...[
            const Gap(16),
            widget.conditionalWidget!,
          ],
          // Conditional text field for "No" selection
          if (_conditionalText != null) ...[
            const Gap(16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Please specify why',
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.black,
                  ),
                ),
                const Gap(4),
                Text(
                  'Optional',
                  style: textTheme.montserratTableSmall.copyWith(
                    color: AppColors.blackTint1,
                    fontStyle: FontStyle.italic,
                  ),
                ),
                const Gap(12),
                TextFormField(
                  controller: _conditionalTextController,
                  onChanged: (value) {
                    setState(() {
                      _conditionalText = value;
                    });
                  },
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'Enter your text here...',
                    hintStyle: textTheme.montserratTitleExtraSmall.copyWith(
                      color: AppColors.blackTint1,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(color: AppColors.blackTint2),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(color: AppColors.blackTint2),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                      borderSide: const BorderSide(
                          color: AppColors.primaryBlue, width: 2),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12.0,
                      vertical: 16.0,
                    ),
                  ),
                  style: textTheme.montserratTitleExtraSmall.copyWith(
                    color: AppColors.black,
                  ),
                ),
              ],
            ),
          ],
          // Camera section
          if (widget.showCameraIcon) ...[
            const Gap(16),
            PhotoUploadWidget(
              selectedImages: widget.selectedImages,
              errorText: widget.photoErrorText,
              onCameraPressed: () {
                if (widget.onCameraTap != null) {
                  widget.onCameraTap!();
                }
              },
              onImagesTap: () {
                if (widget.onCameraTap != null) {
                  widget.onCameraTap!();
                }
              },
            ),
          ],
          if (widget.measurement.required == true && widget.value == null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: Text(
                'This field is required',
                style: textTheme.montserratTableSmall.copyWith(
                  color: Colors.red,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
